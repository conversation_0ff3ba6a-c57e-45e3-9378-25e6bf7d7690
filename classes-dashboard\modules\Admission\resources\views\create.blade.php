@extends('layouts.app')
@section('content')
    <style>
        .firstcap {
            text-transform: capitalize;
        }
    </style>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bs-stepper@1.7.0/dist/css/bs-stepper.min.css" />
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-9">
                    <h1>Admission</h1>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            @if (!isset($data))
                <div class="card mb-4">
                    <div class="card-body">
                        <label class="block mb-2">Enter Mobile Number to Proceed</label>
                        <div class="d-flex gap-3">
                            <input type="text" id="contact_no_check" class="form-control mr-2"
                                placeholder="Enter mobile number" />
                            <button id="checkMobileBtn" class="btn btn-primary">Check</button>
                        </div>
                        <p class="text-danger mt-2 d-none" id="mobile-error">Student not found.</p>
                    </div>
                </div>

                <!-- OTP Box (hidden initially) -->
                <div class="card mb-4 d-none" id="otpBox">
                    <div class="card-body">
                        <label class="block mb-2">Enter 6-digit OTP</label>
                        <div class="d-flex gap-3">
                            <input type="text" id="otp_input" maxlength="6" class="form-control mr-2"
                                placeholder="Enter OTP" />
                            <button id="verifyOtpBtn" class="btn btn-success">Verify OTP</button>
                        </div>
                        <p class="text-danger mt-2 d-none" id="otp-error">Invalid OTP</p>
                    </div>
                </div>
            @endif
            <div id="admissionFormWrapper" class="{{ !isset($data) ? 'd-none' : '' }}">
                <div class="row">
                    <div class="col-md-12">
                        <div class="card card-default">
                            <div class="card-body p-0">
                                <div class="bs-stepper">
                                    <div class="bs-stepper-header" role="tablist">
                                        <div class="step" data-target="#student-details">
                                            <button type="button" class="step-trigger" role="tab"
                                                aria-controls="student-details" id="student-details-trigger">
                                                <span class="bs-stepper-circle">1</span>
                                                <span class="bs-stepper-label">Student Details</span>
                                            </button>
                                        </div>
                                        <div class="line"></div>
                                    </div>
                                    <div class="bs-stepper-content">
                                        @include('Admission::steps.student-details')
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>
@endsection

@section('scripts')
    <script src="https://cdn.jsdelivr.net/npm/bs-stepper@1.7.0/dist/js/bs-stepper.min.js"></script>
    {!! JsValidator::formRequest('Admission\Http\Requests\CreateStudentDetailsRequest', '#student-details-forms') !!}
    <script>
        var admissionCreateRoute = {
            storeStudentDetails: "{{ route('storeStudentDetails') }}",
        };
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            window.stepper = new Stepper(document.querySelector('.bs-stepper'));
        });

        $('#checkMobileBtn').on('click', function() {
            const contactNo = $('#contact_no_check').val().trim();

            if (!contactNo) {
                toastr.warning('Please enter a mobile number');
                return;
            }
            const params = {
                ...doAjax_params_default,
                url: "{{ route('checkStudentByMobile') }}",
                requestType: 'POST',
                data: {
                    contact_no: contactNo
                },
                successCallbackFunction: function(res) {
                    $('#mobile-error').addClass('d-none');
                    $('#otpBox').addClass('d-none');

                    if (res.error && res.no_student) {
                        $('#admissionFormWrapper').removeClass('d-none');
                        $('#contact').val(contactNo);
                        toastr.info('No Student Found, Please create a new entry.');
                        return;
                    }

                    if (res.error) {
                        $('#admissionFormWrapper').addClass('d-none');
                        $('#mobile-error').removeClass('d-none').text(res.message || res.error);
                        return;
                    }

                    if (res.success && res.already_verified) {
                        toastr.info('You already verified your number. Loading student details...');
                        $('#admissionFormWrapper').removeClass('d-none');
                        $('[name="student_id"]').val(res.student.id);

                        populateForm(res.student, res.student.id);
                        if (res.student.get_student_details) {
                            populateForm(res.student.get_student_details, res.student.id);
                        }
                        return;
                    }

                    if (res.success) {
                        toastr.success('Student found. OTP sent.');

                        $('#otpBox').removeClass('d-none');
                        $('#otp_input').val('').focus();
                    } else {
                        $('#admissionFormWrapper').addClass('d-none');
                        $('#mobile-error').removeClass('d-none').text('Student not found.');
                    }
                }
            };

            commonAjax(params);
        });

        $('#verifyOtpBtn').on('click', function() {
            const contactNo = $('#contact_no_check').val().trim();
            const otp = $('#otp_input').val().trim();

            $('#otp-error').addClass('d-none');

            if (!otp || otp.length !== 6 || isNaN(otp)) {
                $('#otp-error').removeClass('d-none').text('Please enter a valid 6-digit OTP');
                return;
            }

            const verifyParams = {
                ...doAjax_params_default,
                url: "{{ route('verifyOtp') }}",
                requestType: 'POST',
                data: {
                    contact_no: contactNo,
                    otp: otp
                },
                successCallbackFunction: function(res) {
                    if (res.success) {
                        toastr.success('OTP verified! Loading student details...');
                        $('#admissionFormWrapper').removeClass('d-none');

                        $('[name="student_id"]').val(res.student.id);

                        populateForm(res.student, res.student.id);
                        if (res.student.get_student_details) {
                            populateForm(res.student.get_student_details, res.student.id);
                        }
                        if (res.academicInfo) {
                            populateForm(res.academicInfo, res.student.id);
                        }

                        $('#otpBox').addClass('d-none');
                    } else {
                        $('#otp-error').removeClass('d-none').text(res.message || 'Invalid OTP');
                    }
                }
            };

            commonAjax(verifyParams);
        });

        function populateForm(data, studentId) {
            console.log('Populating form with data:', data, 'Student ID:', studentId);

            $.each(data, function(key, value) {
                if (key === "photo") {
                    if (studentId) {
                        var nodeServerUrl = "{{ env('UEST_FRONTEND_URL', 'http://localhost:4005') }}";
                        var imageUrlJpg = nodeServerUrl + "/uploads/student/" + studentId + "/studentPhoto.jpg";
                        var imageUrlJpeg = nodeServerUrl + "/uploads/student/" + studentId + "/studentPhoto.jpeg";
                        var imageUrlPng = nodeServerUrl + "/uploads/student/" + studentId + "/studentPhoto.png";

                        // Try to load image in order: jpg -> jpeg -> png -> default
                        $('.image-product-logo').attr('src', imageUrlJpg)
                            .attr('onerror', "this.onerror=null; this.src='" + imageUrlJpeg + "'; this.onerror=function(){this.src='" + imageUrlPng + "'; this.onerror=function(){this.src='http://dummyimage.com/200x200/f5f5f5/000000&text=No+Photo';};}");
                    }
                    return;
                }

                var $field = $('[name="' + key + '"]');

                if ($field.length > 0 && value !== null && value !== undefined && value !== '') {
                    if ($field.is('select')) {
                        $field.val(value);
                        if ($field.hasClass('select2')) {
                            $field.trigger('change');
                        }
                    } else if ($field.hasClass('datepicker')) {
                        $field.val(value);
                    } else {
                        $field.val(value);
                    }
                }
            });
        }

        function disableOtpButton() {
            const $btn = $('#checkMobileBtn');
            let countdown = 60;

            $btn.prop('disabled', true).text(`Wait (${countdown}s)`);

            const interval = setInterval(() => {
                countdown--;
                $btn.text(`Wait (${countdown}s)`);

                if (countdown <= 0) {
                    clearInterval(interval);
                    $btn.prop('disabled', false).text('Check');
                }
            }, 1000);
        }
    </script>
    <script src="{{ asset(mix('js/page-level-js/Admission/js/create.js')) }}"></script>
    @if (isset($data))
        <script>
            $('.firststep').removeClass('d-none');

            var data = {!! json_encode($data) !!};
            var studentId = "{{ $data->getStudent->id }}";
            populateForm(data, studentId);

            var studenteditdata = {!! json_encode($data->getStudent) !!};
            populateForm(studenteditdata, studentId);

            var studenteditprofiledata = {!! json_encode($data->getStudentProfile) !!};
            populateForm(studenteditprofiledata, studentId);

            console.log(data, studenteditdata, studenteditprofiledata)

            $('[name="student_id"]').val("{{ $data->id }}");
            $('#department').trigger('change');

            setTimeout(function() {
                $('#classroom').val("{{ $data->classroom }}").trigger('change');
            }, 2000);
        </script>
    @endif
@endsection
