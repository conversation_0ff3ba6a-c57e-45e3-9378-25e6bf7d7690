<?php

namespace Admission\Repositories;

use Admission\Interfaces\AdmissionInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Admission\Models\Student;
use Admission\Models\StudentAcademicInfo;
use Admission\Models\StudentDetails;
use Admission\Models\StudentDetailsView;
use Fees\Models\StudentPayments;
use Document\Models\Document;
use Illuminate\Support\Str;

class AdmissionRepository implements AdmissionInterface
{
    protected $studentDetails;
    protected $studentAcademicInfo;
    protected $studentPayments;
    protected $studentDetailsView;
    protected $student;

    public function __construct(
        StudentDetails $studentDetails,
        StudentAcademicInfo $studentAcademicInfo,
        StudentPayments $studentPayments,
        StudentDetailsView $studentDetailsView,
        Student $student
    ) {
        $this->studentDetails = $studentDetails;
        $this->studentAcademicInfo = $studentAcademicInfo;
        $this->studentPayments = $studentPayments;
        $this->studentDetailsView = $studentDetailsView;
        $this->student = $student;
    }

    public function getAll($data)
    {
        $list = $this->studentDetailsView::when($data['department'], function ($query) use ($data) {
            return $query->where('department_id', $data['department']);
        })->when($data['classroom'], function ($query) use ($data) {
            return $query->where('classroom_id', $data['classroom']);
        })->when($data['status'], function ($query) use ($data) {
            return $query->where('status', $data['status']);
        })->when(isset($data['search_key']) && $data['search_key'] != "", function ($query) use ($data) {
            return $query->where('first_name', 'like', '%' . $data['search_key'] . '%')
                ->orWhere('last_name', 'like', '%' . $data['search_key'] . '%')
                ->orWhere('middle_name', 'like', '%' . $data['search_key'] . '%')
                ->orWhere('contact_no', 'like', '%' . $data['search_key'] . '%');
        })->when($data['student_id'], function ($query) use ($data) {
            return $query->where('id', $data['student_id']);
        })->where('year_id', getActiveYearId())
            ->where('student_class_uuid', Auth::id())
            ->select(
                'student_details_view.*',
                DB::raw("CONCAT(student_details_view.first_name, ' ' , student_details_view.middle_name , ' ', student_details_view.last_name) AS student_full_name"),
                DB::raw("CONCAT(student_details_view.class_name, ' (', student_details_view.department_name, ')') AS class_info"),
            );

        $concatenatedColumns = ['student_full_name', 'class_info'];
        searchColumn($data->input('columns'), $list, $concatenatedColumns);
        orderColumn($data, $list, 'student_details_view.id');
        
        return $list;
    }

    public function getDatatable($list)
    {
        return datatables()->of($list)
            ->addColumn('photo', function ($data) {
                $nodeServerUrl = env('UEST_FRONTEND_URL', 'http://localhost:4005');
                $imageUrl = $nodeServerUrl . '/uploads/student/' . $data->id . '/studentPhoto.jpg';

                return '<img style="width: 40px !important;height: 40px !important;" src="' . $imageUrl . '" class="img-circle elevation-2">';
            })
            ->addColumn('student_full_name', function ($data) {
                return $data->student_full_name;
            })
            ->addColumn('contact_no', function ($data) {
                return $data->contact_no;
            })
            ->addColumn('year_name', function ($data) {
                return  $data->year_name;
            })
            ->addColumn('class_info', function ($data) {
                return  $data->class_info;
            })
            ->addColumn('action', function ($data) {
                $button = '';
                $button .= '<a href="' . route('studentfee', $data->student_academic_id) . '" class="btn"><i class="fa fa-rupee-sign"></i></a>';

                $button .= '<a href="' . route('student.show', $data->student_academic_id) . '" class="btn"><i class="fas fa-eye"></i></a>';

                $button .= '<a href="' . route('student.edit', $data->student_academic_id) . '" class="btn"><i class="fas fa-edit"></i></a>';

                return $button;
            })->rawColumns(['action', 'photo', 'check_student_transfered'])
            ->make(true);
    }

    public function storeStudentDetails($data)
    {
        DB::beginTransaction();

        try {
            $studentId = $data['student_id'];

            $studentData = [
                'firstName' => $data['firstName'],
                'middleName' => $data['middleName'],
                'lastName' => $data['lastName'],
                'mothersName' => $data['mothersName'],
                'email' => $data['email'] ?? null,
                'contact' => $data['contact'],
                'createdAt' => now(),
                'updatedAt' => now(),
            ];

            $studentiData = Student::updateOrCreate(['contact' => $data['contact']], $studentData);
            
            $photoPath = null;
            if (!empty($data['photo']) && $data['photo'] != 'undefined') {
                $photoPath = $this->uploadImageToNodeServer($data['photo'], $studentiData->id);
            }

            $studentProfileData = [
                'studentId' => $studentiData->id,
                'birthday' => $data['birthday'] ?? null,
                'contactNo2' => $data['contactNo2'] ?? null,
                'photo' => $photoPath,
                'gender' => $data['gender'] ?? null,
                'age' => $data['age'] ?? null,
                'aadhaarNo' => $data['aadhaarNo'] ?? null,
                'bloodGroup' => $data['bloodGroup'] ?? null,
                'birthPlace' => $data['birthPlace'] ?? null,
                'motherTongue' => $data['motherTongue'] ?? null,
                'religion' => $data['religion'] ?? null,
                'caste' => $data['caste'] ?? null,
                'subCaste' => $data['subCaste'] ?? null,
                'medium' => $data['medium'] ?? null,
                'classroom' => $data['classroom_name'] ?? null, // Store classroom name
                'school' => $data['school'] ?? null,
                'address' => $data['address'] ?? null,
                'createdAt' => now(),
                'updatedAt' => now(),
            ];

            StudentDetails::updateOrCreate(['studentId' => $studentiData->id], $studentProfileData);

            $academicInfo = [
                'student_id' => $studentiData->id,
                'department' => $data['department'],
                'classroom' => $data['classroom'],
                'year' => $data['year'],
                'class_uuid' => Auth::id()
            ];

            StudentAcademicInfo::updateOrCreate(['student_id' => $studentiData->id], $academicInfo);

            DB::commit();

            return $studentId;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function findByid($id)
    {
        return $this->studentAcademicInfo::with('getStudent', 'getStudentProfile')->find($id);
    }

    public function searchStudent($data)
    {
        $data['status'] = "ACTIVE";
        $list = $this->getAll($data);
        return $this->getDatatable($list);
    }

    public function activeInactive($student_id)
    {
        $sinfo = $this->studentAcademicInfo->where('id', $student_id)->first();
        $sinfo->status = $sinfo->status == config('constants.STATUS.INACTIVE') ?
            config('constants.STATUS.ACTIVE') : config('constants.STATUS.INACTIVE');
        $sinfo->save();
    }

    public function getAllStudentByClassAndDepartment($data)
    {
        $students = $this->studentDetailsView
            ->where('department_id', $data['department_id'])
            ->where('classroom_id', $data['classroom_id'])
            ->where('status', 'ACTIVE')->where('year_id', getActiveYearId())->get();
        return $students;
    }

    public function checkStudentTransfered($gr_no)
    {
        $activeYear = getActiveYearId();
        $studentDetails = $this->studentDetailsView::where('gr_no', $gr_no)->where('year_id', '>', $activeYear)->exists();

        return $studentDetails;
    }

    public function updateRoutes($data, $id)
    {
        $this->studentAcademicInfo::updateOrCreate(
            ['student_id' => $id],
            [
                'waypoint' => $data['waypoint'],
                'route' => $data['route']
            ]
        );
    }

    public function getStudentDocuments($studentId)
    {
        return Document::where('student_id', $studentId)->get();
    }

    public function getAllStudentsRaw($classUuid = null, $search = null, $yearName = null)
    {
        $perPage = 10;
        $page = request()->query('page', 1);

        $query = $this->studentDetailsView::select(
            'student_details_view.*',
            DB::raw("CONCAT(student_details_view.first_name, ' ', student_details_view.middle_name, ' ', student_details_view.last_name) AS student_full_name"),
            DB::raw("CONCAT(student_details_view.class_name, ' (', student_details_view.department_name, ')') AS class_info")
        );

        if ($classUuid) {
            $query->where('student_class_uuid', $classUuid);
        }

        if ($search) {
            $search = strtolower($search);
            $query->where(function ($q) use ($search) {
                $q->whereRaw("LOWER(CONCAT(student_details_view.first_name, ' ', student_details_view.middle_name, ' ', student_details_view.last_name)) LIKE ?", ['%' . $search . '%'])
                    ->orWhereRaw("LOWER(student_details_view.email) LIKE ?", ['%' . $search . '%']);
            });
        }

        if ($yearName) {
            $query->where('year_name', $yearName);
        }

        $students = $query->paginate($perPage, ['*'], 'page', $page);

        return $students;
    }

    public function checkByMobile($contact_no)
    {
        $student = Student::with('getStudentDetails')
            ->where('contact', $contact_no)
            ->first();

        $academicInfo = null;

        if ($student) {
            $academicInfo = StudentAcademicInfo::with('getDepartment', 'getClassroom', 'getYear', 'getClass')->where('student_id', $student->id)
                ->where('status', 'ACTIVE')
                ->first();
        }

        return [
            'student' => $student,
            'academicInfo' => $academicInfo,
        ];
    }
    private function uploadImageToNodeServer($file, $studentId)
    {
        try {
            $nodeApiUrl = env('NODE_API_URL', 'http://localhost:4005/api/v1');

            $response = Http::attach(
                'photo',
                file_get_contents($file->getRealPath()),
                $file->getClientOriginalName()
            )->post("{$nodeApiUrl}/student-profile/simple-upload/{$studentId}");

            if ($response->successful()) {
                $responseData = $response->json();
                if ($responseData['success']) {
                    return $responseData['data']['photoPath'];
                }
            }

            return null;

        } catch (\Exception $e) {
            return null;
        }
    }

}
